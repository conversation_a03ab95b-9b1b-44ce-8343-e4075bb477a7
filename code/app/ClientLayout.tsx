"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";

// 用户状态类型
interface User {
  username: string;
}

// 用户上下文类型
interface UserContextType {
  user: User | null;
  loading: boolean;
  login: (token: string) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<void>;
}

// 创建用户上下文
const UserContext = createContext<UserContextType | undefined>(undefined);

// 用户状态提供者组件
export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // 检查认证状态
  const checkAuth = async () => {
    const token = localStorage.getItem("token");
    if (!token) {
      setUser(null);
      setLoading(false);
      return;
    }

    try {
      const res = await fetch("/api/auth/me", {
        headers: { Authorization: "Bearer " + token },
      });

      if (!res.ok) {
        throw new Error("未登录");
      }

      const data = await res.json();
      setUser(data.user);
    } catch (err) {
      console.log("认证失败:", err);
      localStorage.removeItem("token");
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  // 登录
  const login = async (token: string) => {
    localStorage.setItem("token", token);
    await checkAuth();
  };

  // 登出
  const logout = () => {
    localStorage.removeItem("token");
    setUser(null);
    router.push("/");
  };

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuth();
  }, []);

  const value = {
    user,
    loading,
    login,
    logout,
    checkAuth,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}

// 使用用户上下文的 Hook
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}

// 客户端布局组件，处理鉴权和用户信息
export default function ClientLayout({ children }: { children: React.ReactNode }) {
  const { user, loading, logout } = useUser();
  const pathname = usePathname();

  // 如果未登录且不在首页，跳转到首页
  useEffect(() => {
    if (!loading && !user && pathname !== "/") {
      window.location.href = "/";
    }
  }, [user, loading, pathname]);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div>加载中...</div>
      </div>
    );
  }

  // 导航项定义
  const navItems = [
    { name: "仪表盘", path: "/dashboard" },
    { name: "二维码管理", path: "/qrcodes" },
    { name: "订阅者管理", path: "/subscribers" },
    { name: "通知日志", path: "/notifications" },
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-blue-600 text-white shadow-md">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <h1 className="text-xl font-bold">微信二维码通知系统</h1>
          {user && (
            <div className="flex items-center">
              <span className="mr-4">管理员: {user.username}</span>
              <button
                className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded transition-colors"
                onClick={logout}
              >
                登出
              </button>
            </div>
          )}
        </div>
      </header>

      {user && (
        <nav className="bg-white shadow-sm">
          <div className="container mx-auto px-4">
            <ul className="flex space-x-1">
              {navItems.map((item) => (
                <li key={item.path}>
                  <Link
                    href={item.path}
                    className={`inline-block px-4 py-3 font-medium transition-colors ${
                      pathname === item.path
                        ? "text-blue-600 border-b-2 border-blue-600"
                        : "text-gray-600 hover:text-blue-600 hover:bg-gray-50"
                    }`}
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </nav>
      )}

      <main className="flex-grow container mx-auto px-4 py-6">
        {children}
      </main>

      <footer className="bg-gray-100 border-t border-gray-200 py-4">
        <div className="container mx-auto px-4 text-center text-gray-500 text-sm">
          © {new Date().getFullYear()} 微信二维码通知系统
        </div>
      </footer>
    </div>
  );
}
