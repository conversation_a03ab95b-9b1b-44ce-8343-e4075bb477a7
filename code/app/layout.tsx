import React from "react";
import ClientLayout, { UserProvider } from "./ClientLayout";
import "./globals.css";

// 服务器组件，必须包含 <html> 和 <body>
export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="zh-CN">
      <body className="bg-gray-50 min-h-screen">
        <UserProvider>
          <ClientLayout>{children}</ClientLayout>
        </UserProvider>
      </body>
    </html>
  );
}
